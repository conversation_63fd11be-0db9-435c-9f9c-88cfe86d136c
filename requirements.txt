# Core dependencies
asyncio-mqtt==0.16.1
aiohttp==3.9.1
websockets==12.0

# Solana blockchain integration
solana==0.32.0
solders==0.20.0
anchorpy==0.19.1

# Data analysis and scientific computing
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4
scikit-learn==1.3.2

# Technical analysis
ta-lib==0.4.28
pandas-ta==0.3.14b0
mplfinance==0.12.10b0

# Web scraping and API clients
requests==2.31.0
beautifulsoup4==4.12.2
selenium==4.16.0
tweepy==4.14.0

# Sentiment analysis and NLP
textblob==0.17.1
vaderSentiment==3.3.2
transformers==4.36.2
torch==2.1.2

# Database and caching
redis==5.0.1
sqlite3  # Built-in Python module
sqlalchemy==2.0.23

# Configuration and environment
python-dotenv==1.0.0
pydantic==2.5.2
pyyaml==6.0.1

# Logging and monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0

# Development tools
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Jupyter for analysis
jupyter==1.0.0
ipykernel==6.27.1

# Visualization
matplotlib==3.8.2
plotly==5.17.0
seaborn==0.13.0

# HTTP and API utilities
httpx==0.25.2
fastapi==0.104.1
uvicorn==0.24.0

# Cryptography and security
cryptography==41.0.8
pycryptodome==3.19.0

# Time and scheduling
schedule==1.2.0
python-crontab==3.0.0

# Mathematical and statistical libraries
statsmodels==0.14.0
quantlib==1.32

# WebSocket clients for real-time data
python-socketio==5.10.0
websocket-client==1.6.4

# Jupiter aggregator SDK (if available)
# jupiter-python-sdk==0.1.0  # Uncomment if available

# Additional utilities
click==8.1.7
rich==13.7.0
tqdm==4.66.1
