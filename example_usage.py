#!/usr/bin/env python3
"""
Example usage of the Solana Memecoin Trading Agent
Demonstrates how to use the multi-agent system for trading analysis
"""

import asyncio
import logging
from datetime import datetime
from main_agent import MainAgent, TradingSignal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def demo_market_analysis():
    """Demonstrate market analysis capabilities"""
    print("🚀 Solana Memecoin Trading Agent Demo")
    print("=" * 50)
    
    # Initialize the main agent
    agent = MainAgent()
    
    try:
        # Start the agent system
        print("📡 Starting agent system...")
        await agent.start()
        print("✅ All agents initialized successfully")
        
        # List of popular memecoins to analyze
        symbols = ["BONK", "WIF", "PEPE", "SHIB"]
        
        print(f"\n📊 Analyzing {len(symbols)} memecoin opportunities...")
        
        # Analyze each symbol
        signals = []
        for symbol in symbols:
            print(f"\n🔍 Analyzing {symbol}...")
            try:
                signal = await agent.analyze_market_opportunity(symbol)
                signals.append(signal)
                
                # Display signal information
                print(f"   Action: {signal.action.upper()}")
                print(f"   Confidence: {signal.confidence:.2%}")
                print(f"   Position Size: {signal.position_size:.2%}")
                if signal.price_target:
                    print(f"   Price Target: ${signal.price_target:.4f}")
                if signal.stop_loss:
                    print(f"   Stop Loss: ${signal.stop_loss:.4f}")
                print(f"   Reasoning: {signal.reasoning}")
                
            except Exception as e:
                print(f"   ❌ Error analyzing {symbol}: {str(e)}")
        
        # Display summary
        print(f"\n📈 Analysis Summary")
        print("-" * 30)
        
        buy_signals = [s for s in signals if s.action == "buy"]
        sell_signals = [s for s in signals if s.action == "sell"]
        hold_signals = [s for s in signals if s.action == "hold"]
        
        print(f"🟢 Buy Signals: {len(buy_signals)}")
        print(f"🔴 Sell Signals: {len(sell_signals)}")
        print(f"🟡 Hold Signals: {len(hold_signals)}")
        
        # Show best opportunities
        if buy_signals:
            best_buy = max(buy_signals, key=lambda x: x.confidence)
            print(f"\n🎯 Best Buy Opportunity: {best_buy.symbol}")
            print(f"   Confidence: {best_buy.confidence:.2%}")
            print(f"   Recommended Position: {best_buy.position_size:.2%}")
        
        # Simulate trade execution for best signal
        if buy_signals and best_buy.confidence > 0.6:
            print(f"\n💰 Simulating trade execution for {best_buy.symbol}...")
            try:
                result = await agent.execute_trade(best_buy)
                print(f"   ✅ Trade simulation result: {result}")
            except Exception as e:
                print(f"   ❌ Trade simulation failed: {str(e)}")
        
    except Exception as e:
        logger.error(f"Demo failed: {str(e)}")
        print(f"❌ Demo failed: {str(e)}")
    
    finally:
        # Clean shutdown
        print("\n🛑 Shutting down agent system...")
        await agent.stop()
        print("✅ Agent system stopped successfully")

async def demo_risk_management():
    """Demonstrate risk management features"""
    print("\n🛡️  Risk Management Demo")
    print("=" * 30)
    
    agent = MainAgent()
    await agent.start()
    
    try:
        # Simulate portfolio with different risk scenarios
        test_scenarios = [
            {"symbol": "BONK", "position_size": 0.05, "scenario": "Conservative"},
            {"symbol": "WIF", "position_size": 0.15, "scenario": "Aggressive"},
            {"symbol": "PEPE", "position_size": 0.25, "scenario": "High Risk"},
        ]
        
        for scenario in test_scenarios:
            print(f"\n📊 {scenario['scenario']} Scenario - {scenario['symbol']}")
            
            # Get risk assessment
            risk_data = await agent.delegate_task(
                agent.AgentType.RISK_MANAGEMENT,
                {
                    "symbol": scenario["symbol"],
                    "requested_position": scenario["position_size"],
                    "action": "assess_risk"
                }
            )
            
            print(f"   Requested Position: {scenario['position_size']:.1%}")
            print(f"   Recommended Position: {risk_data.get('recommended_position_size', 0):.1%}")
            print(f"   Risk Score: {risk_data.get('score', 0):.2f}")
            print(f"   Stop Loss: {risk_data.get('stop_loss', 0):.2%}")
            
    finally:
        await agent.stop()

async def demo_sentiment_analysis():
    """Demonstrate sentiment analysis capabilities"""
    print("\n📱 Sentiment Analysis Demo")
    print("=" * 30)
    
    agent = MainAgent()
    await agent.start()
    
    try:
        symbols = ["BONK", "WIF"]
        
        for symbol in symbols:
            print(f"\n🔍 Sentiment analysis for {symbol}...")
            
            sentiment_data = await agent.delegate_task(
                agent.AgentType.NEWS_SENTIMENT,
                {
                    "symbol": symbol,
                    "lookback_hours": 24,
                    "sources": ["twitter", "discord", "telegram"]
                }
            )
            
            print(f"   Sentiment Score: {sentiment_data.get('score', 0):.2f}")
            print(f"   Overall Sentiment: {sentiment_data.get('sentiment', 'neutral')}")
            print(f"   Mentions (24h): {sentiment_data.get('mentions', 0)}")
            
    finally:
        await agent.stop()

async def main():
    """Run all demo functions"""
    print("🎮 Solana Memecoin Trading Agent - Complete Demo")
    print("=" * 60)
    
    # Run different demo scenarios
    await demo_market_analysis()
    await demo_risk_management()
    await demo_sentiment_analysis()
    
    print("\n🎉 Demo completed successfully!")
    print("\nNext steps:")
    print("1. Configure your API keys in environment variables")
    print("2. Set up your Solana wallet connection")
    print("3. Adjust risk parameters in agent_config.json")
    print("4. Start with small position sizes for testing")
    print("5. Monitor performance and adjust strategies")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {str(e)}")
        logger.exception("Demo failed")
