{"main_agent": {"name": "Solana Memecoin Trading Coordinator", "version": "1.0.0", "max_context_length": 32000, "decision_threshold": 0.75, "risk_tolerance": "moderate", "max_concurrent_tasks": 6}, "sub_agents": {"market_analysis": {"name": "Market Analysis Agent", "context_window": 8000, "update_frequency": "30s", "data_sources": ["jupiter_api", "raydium_api", "coingecko_api", "birdeye_api"], "tools": ["web_scraping", "api_calls", "data_analysis", "chart_generation"], "permissions": {"market_data": "read", "wallet_access": "none", "external_apis": "price_volume_only", "risk_limits": "none"}, "performance_metrics": {"prediction_accuracy_target": 0.65, "signal_quality_threshold": 0.7, "response_time_max": "5s"}}, "risk_management": {"name": "Risk Management Agent", "context_window": 6000, "update_frequency": "real_time", "max_portfolio_risk": 0.02, "max_position_size": 0.1, "stop_loss_default": 0.05, "tools": ["portfolio_calculator", "risk_metrics", "historical_analysis", "monte_carlo"], "permissions": {"market_data": "read", "wallet_access": "read_only", "external_apis": "none", "risk_limits": "full_access"}, "safety_limits": {"max_daily_loss": 0.05, "max_drawdown": 0.15, "position_concentration": 0.25, "leverage_limit": 1.0}}, "technical_analysis": {"name": "Technical Analysis Agent", "context_window": 10000, "update_frequency": "1m", "timeframes": ["1m", "5m", "15m", "1h", "4h", "1d"], "indicators": ["RSI", "MACD", "Bollinger_Bands", "EMA", "Volume_Profile", "Support_Resistance"], "tools": ["charting_libraries", "indicator_calculations", "pattern_recognition", "backtesting"], "permissions": {"market_data": "read", "wallet_access": "none", "external_apis": "chart_data_only", "risk_limits": "none"}, "signal_thresholds": {"strong_buy": 0.8, "buy": 0.6, "neutral": 0.5, "sell": 0.4, "strong_sell": 0.2}}, "wallet_management": {"name": "Wallet Management Agent", "context_window": 4000, "update_frequency": "on_demand", "supported_wallets": ["phantom", "solflare", "backpack", "glow"], "tools": ["solana_web3js", "wallet_adapter", "transaction_builder", "gas_optimizer"], "permissions": {"market_data": "none", "wallet_access": "full", "external_apis": "blockchain_rpc_only", "risk_limits": "transaction_limits"}, "security_settings": {"require_confirmation": true, "max_transaction_value": 1000, "multi_sig_threshold": 500, "gas_limit_multiplier": 1.2}}, "news_sentiment": {"name": "News & Sentiment Agent", "context_window": 12000, "update_frequency": "2m", "sentiment_sources": ["twitter_api", "discord_webhooks", "telegram_monitoring", "reddit_api"], "tools": ["social_media_apis", "sentiment_analysis", "news_aggregation", "nlp_processing"], "permissions": {"market_data": "none", "wallet_access": "none", "external_apis": "social_media_only", "risk_limits": "rate_limits"}, "sentiment_weights": {"twitter": 0.4, "discord": 0.3, "telegram": 0.2, "reddit": 0.1}, "influence_tracking": {"track_influencers": true, "min_follower_count": 10000, "engagement_threshold": 0.05}}, "execution": {"name": "Execution Agent", "context_window": 5000, "update_frequency": "real_time", "supported_dexs": ["jupiter", "raydium", "orca", "serum"], "tools": ["jupiter_api", "dex_apis", "slippage_calculator", "order_management"], "permissions": {"market_data": "read", "wallet_access": "full", "external_apis": "dex_apis_only", "risk_limits": "position_limits"}, "execution_settings": {"max_slippage": 0.01, "retry_attempts": 3, "timeout_seconds": 30, "priority_fee": "auto"}, "order_types": ["market", "limit", "stop_loss", "take_profit", "dca"]}}, "communication": {"message_format": "json", "encryption": "aes_256", "timeout_seconds": 30, "retry_attempts": 3, "heartbeat_interval": "10s"}, "logging": {"level": "info", "retention_days": 30, "audit_trail": true, "performance_metrics": true}, "emergency_procedures": {"circuit_breaker_loss": 0.1, "emergency_exit_enabled": true, "manual_override": true, "notification_channels": ["email", "sms", "discord"]}}